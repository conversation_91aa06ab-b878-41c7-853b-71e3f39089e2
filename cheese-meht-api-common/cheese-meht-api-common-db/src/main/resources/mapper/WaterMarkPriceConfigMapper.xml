<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.WaterMarkPriceConfigMapper">
  <resultMap id="BaseResultMap" type="com.coocaa.meht.api.cheese.common.db.entity.WaterMarkPriceConfigEntity">
    <!--@mbg.generated-->
    <!--@Table water_mark_price_config-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="building_type" jdbcType="TINYINT" property="buildingType" />
    <result column="city_id" jdbcType="INTEGER" property="cityId" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="core_area_type" jdbcType="TINYINT" property="coreAreaType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="screen_type" jdbcType="TINYINT" property="screenType" />
    <result column="water_level_price" jdbcType="DECIMAL" property="waterLevelPrice" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, building_type, city_id, city_name, core_area_type, create_time, creator, screen_type,
    water_level_price
  </sql>
</mapper>