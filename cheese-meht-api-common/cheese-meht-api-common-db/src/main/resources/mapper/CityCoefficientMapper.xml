<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.CityCoefficientMapper">
    
    <resultMap id="BaseResultMap" type="com.coocaa.meht.api.cheese.common.db.entity.CityCoefficientEntity">
        <id column="id" property="id"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="region" property="region"/>
        <result column="ad_code" property="adCode"/>
        <result column="coefficient" property="coefficient"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="selectByAdCode" resultMap="BaseResultMap">
        SELECT * FROM city_coefficient 
        WHERE ad_code = #{adCode} 
        AND deleted = 0 
        LIMIT 1
    </select>
</mapper> 