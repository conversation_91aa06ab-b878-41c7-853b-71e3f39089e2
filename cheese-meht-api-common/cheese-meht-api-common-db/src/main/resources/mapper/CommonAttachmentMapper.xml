<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.CommonAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.coocaa.meht.api.cheese.common.db.entity.CommonAttachmentEntity">
    <!--@mbg.generated-->
    <!--@Table common_attachment-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="attachment_type" jdbcType="VARCHAR" property="attachmentType" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="biz_id" jdbcType="INTEGER" property="bizId" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="extend_data" jdbcType="VARCHAR" property="extendData" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="size" jdbcType="BIGINT" property="size" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="url" jdbcType="VARCHAR" property="url" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, attachment_type, biz_code, biz_id, business_type, create_by, create_time, extend_data, 
    `name`, `size`, `type`, update_by, update_time, url
  </sql>
</mapper>