<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.PointPicContractSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.coocaa.meht.api.cheese.common.db.entity.PointPicContractSnapshotEntity">
        <id column="id" property="id" />
        <result column="point_id" property="pointId" />
        <result column="pic" property="pic" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, point_id, pic, create_by, create_time
    </sql>

</mapper> 