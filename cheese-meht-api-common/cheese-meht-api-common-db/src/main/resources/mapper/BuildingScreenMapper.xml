<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.BuildingScreenMapper">
  <resultMap id="BaseResultMap" type="com.coocaa.meht.api.cheese.common.db.entity.BuildingScreenEntity">
    <!--@mbg.generated-->
    <!--@Table building_screen-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="building_ceiling_height" jdbcType="DECIMAL" property="buildingCeilingHeight" />
    <result column="building_rating_no" jdbcType="VARCHAR" property="buildingRatingNo" />
    <result column="building_spacing" jdbcType="DECIMAL" property="buildingSpacing" />
    <result column="company_count" jdbcType="INTEGER" property="companyCount" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="elevator_count" jdbcType="INTEGER" property="elevatorCount" />
    <result column="final_coefficient" jdbcType="DECIMAL" property="finalCoefficient" />
    <result column="spec" jdbcType="VARCHAR" property="spec" />
    <result column="special_desc" jdbcType="LONGVARCHAR" property="specialDesc" />
    <result column="submit_coefficient" jdbcType="DECIMAL" property="submitCoefficient" />
    <result column="total_building_count" jdbcType="INTEGER" property="totalBuildingCount" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, building_ceiling_height, building_rating_no, building_spacing, company_count, 
    create_by, create_time, elevator_count, final_coefficient, spec, special_desc, submit_coefficient, 
    total_building_count, update_by, update_time
  </sql>
</mapper>