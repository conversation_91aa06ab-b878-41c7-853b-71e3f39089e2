<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.PriceApplyMapper">


    <select id="priceApplyPage" resultType="com.coocaa.meht.api.cheese.common.db.dto.PriceApplyDTO">
        SELECT
        pa.id,
        pa.apply_code,
        pa.building_no,
        pa.approve_by,
        pa.approve_time,
        pa.building_name,
        pa.create_by,
        pa.create_time,
        pa.`status`,
        CASE
        pa.`status`
        WHEN 0 THEN
        '草稿'
        WHEN 1 THEN
        '待审核'
        WHEN 2 THEN
        '已审核'
        WHEN 3 THEN
        '审核不通过' ELSE ''
        END AS statusName,
        pa.contract_duration,
        pa.total_amount,
        pa.payment_type,
        pa.large_screen_flag,
        CASE
        pa.large_screen_flag
        WHEN 1 THEN
        '小屏'
        WHEN 2 THEN
        '大屏'
        WHEN 3 THEN
        '大小屏' ELSE ''
        END AS largeScreenName,
        pa.project_level,
        CASE
        pa.`building_type`
        WHEN 0 THEN
        '写字楼'
        WHEN 1 THEN
        '商住楼'
        WHEN 2 THEN
        '综合体'
        WHEN 3 THEN
        '产业园区' ELSE ''
        END AS building_type,
        pa.map_city,
        pa.map_address,
        pa.final_coefficient,
        pa.location_name
        FROM
        price_apply pa
        <where>
            pa.status IN (1,2,3)
            <if test="condition.applyCode != null and condition.applyCode!=''">
                AND pa.apply_code LIKE concat('%',#{condition.applyCode},'%')
            </if>

            <if test="condition.buildingName != null and condition.buildingName!=''">
                AND pa.building_name LIKE concat('%',#{condition.buildingName},'%')
            </if>

            <if test="condition.startTime != null and condition.startTime!='' and condition.endTime !=null and condition.endTime !=''">
                AND pa.create_time BETWEEN #{condition.startTime} and #{condition.endTime}
            </if>

            <if test="condition.approveStartTime != null and condition.approveStartTime!='' and condition.approveEndTime !=null and condition.approveEndTime !=''">
                AND pa.approve_time BETWEEN #{condition.approveStartTime} and #{condition.approveEndTime}
                and pa.status = 2
            </if>

            <if test="condition.projectLevel != null and condition.projectLevel!=''">
                AND pa.project_level = #{condition.projectLevel}
            </if>

            <if test="condition.largeScreenFlag != null and condition.largeScreenFlag!=''">
                AND pa.large_screen_flag = #{condition.largeScreenFlag}
            </if>
            <if test="condition.status != null and condition.status!=''">
                AND pa.status = #{condition.status}
            </if>
            <if test="condition.buildingType != null and condition.buildingType!=''">
                AND pa.building_type = #{condition.buildingType}
            </if>

            <if test="condition.cityNames != null and condition.cityNames.size()>0">
                AND pa.map_city IN
                <foreach collection="condition.cityNames" item="city" open="(" separator="," close=")">
                    #{city}
                </foreach>
            </if>

            <if test="condition.submitUsers != null and condition.submitUsers.size()>0">
                AND pa.create_by IN
                <foreach collection="condition.submitUsers" item="user" open="(" separator="," close=")">
                    #{user}
                </foreach>
            </if>

        order by pa.id DESC

        </where>

    </select>

    <select id="getApplyPoint" resultType="com.coocaa.meht.api.cheese.common.db.dto.ApplyPointDTO">
        SELECT
            padp.apply_id applyId,
            padp.price_apply_device_id deviceId,
            padp.point_code code,
            p.device_size deviceSize
        FROM
            price_apply_device_point padp
                LEFT JOIN point p ON padp.point_code = p.`code`
        <where>
            padp.apply_id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>



    <select id="priceApplyList" resultType="com.coocaa.meht.api.cheese.common.db.dto.PriceApplyDTO">
        SELECT
        pa.id,
        pa.apply_code,
        pa.building_no,
        pa.approve_by,
        pa.approve_time,
        pa.building_name,
        pa.create_by,
        pa.create_time,
        pa.`status`,
        CASE
        pa.`status`
        WHEN 0 THEN
        '草稿'
        WHEN 1 THEN
        '待审核'
        WHEN 2 THEN
        '已审核'
        WHEN 3 THEN
        '审核不通过' ELSE ''
        END AS statusName,
        pa.contract_duration,
        pa.total_amount,
        pa.payment_type,
        pa.large_screen_flag,
        CASE
        pad.large_screen_flag
        WHEN 1 THEN
        '小屏'
        WHEN 2 THEN
        '大屏'
        WHEN 3 THEN
        '大小屏' ELSE ''
        END AS largeScreenName,
        pa.project_level,
        CASE
        pa.`building_type`
        WHEN 0 THEN
        '写字楼'
        WHEN 1 THEN
        '商住楼'
        WHEN 2 THEN
        '综合体'
        WHEN 3 THEN
        '产业园区' ELSE ''
        END AS building_type,
        pa.map_city,
        pa.map_address,
        pa.small_water_mark_price,
        pa.big_water_mark_price,
        pad.quantity,
        pad.incentive_price,
        pad.sign_price,
        pad.core_area_flag,
        pad.id as deviceId,
        pa.final_coefficient,
        pa.location_name
        FROM
        price_apply pa
        LEFT JOIN  price_apply_device pad ON pa.id = pad.apply_id
        <where>
            pa.status IN (1,2,3)
            <if test="condition.applyCode != null and condition.applyCode!=''">
                AND pa.apply_code LIKE concat('%',#{condition.applyCode},'%')
            </if>

            <if test="condition.buildingName != null and condition.buildingName!=''">
                AND pa.building_name LIKE concat('%',#{condition.buildingName},'%')
            </if>

            <if test="condition.startTime != null and condition.startTime!='' and condition.endTime !=null and condition.endTime !=''">
                AND pa.create_time BETWEEN #{condition.startTime} and #{condition.endTime}
            </if>

            <if test="condition.approveStartTime != null and condition.approveStartTime!='' and condition.approveEndTime !=null and condition.approveEndTime !=''">
                AND pa.approve_time BETWEEN #{condition.approveStartTime} and #{condition.approveEndTime}
                and pa.status = 2
            </if>

            <if test="condition.status != null and condition.status!=''">
                AND pa.status = #{condition.status}
            </if>

            <if test="condition.projectLevel != null and condition.projectLevel!=''">
                AND pa.project_level = #{condition.projectLevel}
            </if>

            <if test="condition.buildingType != null and condition.buildingType!=''">
                AND pa.building_type = #{condition.buildingType}
            </if>

            <if test="condition.largeScreenFlag != null and condition.largeScreenFlag!=''">
                AND pa.large_screen_flag = #{condition.largeScreenFlag}
            </if>

            <if test="condition.cityNames != null and condition.cityNames.size()>0">
                AND pa.map_city IN
                <foreach collection="condition.cityNames" item="city" open="(" separator="," close=")">
                    #{city}
                </foreach>
            </if>

            <if test="condition.submitUsers != null and condition.submitUsers.size()>0">
                AND pa.create_by IN
                <foreach collection="condition.submitUsers" item="user" open="(" separator="," close=")">
                    #{user}
                </foreach>
            </if>

            order by pa.id DESC

        </where>

    </select>

</mapper>