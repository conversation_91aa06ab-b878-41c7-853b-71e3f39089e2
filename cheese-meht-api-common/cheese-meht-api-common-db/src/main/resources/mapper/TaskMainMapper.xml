<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.TaskMainMapper">
    <resultMap id="BaseResultMap" type="com.coocaa.meht.api.cheese.common.db.entity.TaskMainEntity">
        <id column="id" property="id"/>
        <result column="task_no" property="taskNo"/>
        <result column="task_name" property="taskName"/>
        <result column="create_by_no" property="createByNo"/>
        <result column="task_status" property="taskStatus"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
</mapper> 