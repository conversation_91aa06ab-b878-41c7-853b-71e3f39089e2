<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.CustomerFollowRecordMapper">

    <select id="selectPage" resultType="com.coocaa.meht.api.cheese.common.db.dto.CustomerFollowRecordListDto">
        SELECT
        cfr.id,
        cfr.visit_time,
        cfr.visit_type,
        cfr.visit_objects,
        cfr.business_code,
        cfr.visit_purpose,
        cfr.visit_result,
        cfr.batch_id,
        cfr.create_by,
        cfr.create_time,
        cfr.update_by,
        cfr.update_time,
        cfr.deleted,
        cfr.role,
        cfr.valid,
        cfr.business_type,
        cfr.phone ,
        bo.`name` businessName ,
        ba.building_name building_name,
        ba.map_city city
        FROM
        customer_follow_record cfr
        LEFT JOIN business_opportunity bo on bo.`code` = cfr.business_code
        LEFT JOIN building_rating ba on ba.building_no = bo.building_no
        <where>
            <if test="query.businessCode != null and query.businessCode != ''">
                AND cfr.business_code like concat('%',#{query.businessCode},'%')
            </if>
            <if test="query.businessName != null and query.businessName != ''">
                AND bo.name like concat('%',#{query.businessName},'%')
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND ba.building_name like concat('%',#{query.projectName},'%')
            </if>
            <if test="query.visitType != null and query.visitType != ''">
                AND cfr.visit_type = #{query.visitType}
            </if>
            <if test='query.createBy != null and query.createBy.size() > 0'>
                AND cfr.create_by IN
                <foreach collection='query.createBy' item='code' open='(' separator=',' close=')'>
                    #{code}
                </foreach>
            </if>
            <if test='query.cities != null and query.cities.size() > 0'>
                AND ba.map_city IN
                <foreach collection='query.cities' item='code' open='(' separator=',' close=')'>
                    #{code}
                </foreach>
            </if>
            <if test="query.valid != null">
                AND cfr.valid = #{query.valid}
            </if>

            <if test="query.businessType != null">
                AND cfr.business_type = #{query.businessType}
            </if>
            <if test="query.followTimeStart != null and query.followTimeStart != ''">
                AND cfr.visit_time &gt;= #{query.followTimeStart}
            </if>
            <if test="query.followTimeEnd != null and query.followTimeEnd != ''">
                AND cfr.visit_time &lt;= #{query.followTimeEnd}
            </if>

            <if test="query.createTimeStart != null and query.createTimeStart != ''">
                AND cfr.create_time &gt;= #{query.createTimeStart}
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND cfr.create_time &lt;= #{query.createTimeEnd}
            </if>
            and cfr.deleted = 0
            order by cfr.visit_time desc
        </where>
    </select>

    <select id="queryProjectName" resultType="com.coocaa.meht.api.cheese.common.tools.bean.common.CodeNameVO">
        SELECT
        distinct
        ba.building_name name
        FROM
        customer_follow_record cfr
        LEFT JOIN business_opportunity bo on bo.`code` = cfr.business_code
        LEFT JOIN building_rating ba on ba.building_no = bo.building_no
        <where>
            <if test="name != null and name != ''">
                AND ba.building_name like concat('%',#{name},'%')
            </if>
        </where>
    </select>
    <select id="queryProjectCity" resultType="com.coocaa.meht.api.cheese.common.tools.bean.common.CodeNameVO">
        SELECT
        distinct
        ba.map_city name
        FROM
        customer_follow_record cfr
        LEFT JOIN business_opportunity bo on bo.`code` = cfr.business_code
        LEFT JOIN building_rating ba on ba.building_no = bo.building_no
        <where>
            <if test="name != null and name != ''">
                AND ba.map_city like concat('%',#{name},'%')
            </if>
        </where>
    </select>
</mapper>