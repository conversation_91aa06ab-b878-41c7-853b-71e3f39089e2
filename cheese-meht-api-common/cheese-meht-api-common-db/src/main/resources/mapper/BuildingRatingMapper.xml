<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.BuildingRatingMapper">


    <select id="applyList" resultType="com.coocaa.meht.api.cheese.common.db.entity.BuildingRatingEntity">
        select
        br.id,
        br.building_no,
        br.building_status,
        br.`status`,
        br.building_type,
        br.building_name,
        br.map_no,
        br.map_province,
        br.map_city,
        br.map_region,
        br.map_address,
        br.map_latitude,
        br.map_longitude,
        br.map_ad_code,
        br.authentication_start,
        br.authentication_end,
        br.authentication_period,
        br.freeze_start,
        br.freeze_end,
        br.freeze_period,
        br.building_desc,
        br.submit_user,
        br.submit_time,
        br.building_score,
        br.project_level,
        br.project_review_level,
        br.approve_user,
        br.approve_time,
        br.approve_desc,
        br.reject_user,
        br.reject_time,
        br.reject_desc,
        br.create_time,
        br.project_ai_level,
        br.building_ai_score,
        br.target_point_count,
        bm.building_meta_no as bc_building_no,
        su.real_name as submit_user_name,
        ap.emp_name as submit_user_name_ep
        from building_rating br
        left join building_meta bm on br.map_no=bm.map_no
        left join sys_user su on su.emp_code=br.submit_user
        left join agent_personnel ap on ap.emp_code=br.submit_user
        <where>
            AND br.`status` != 5
            <if test="condition.buildingNo != null and condition.buildingNo!=''">
                AND br.building_no = #{condition.buildingNo}
            </if>
            <if test="condition.buildingName != null and condition.buildingName!=''">
                AND br.building_name LIKE CONCAT('%',#{condition.buildingName},'%')
            </if>
            <if test="condition.bcBuildingNo != null and condition.bcBuildingNo!=''">
                AND bm.building_meta_no = #{condition.bcBuildingNo}
            </if>
            <if test="condition.projectLevel != null and condition.projectLevel!=''">
                AND br.project_level = #{condition.projectLevel}
            </if>
            <if test="condition.projectAiLevel != null and condition.projectAiLevel!=''">
                AND br.project_ai_level = #{condition.projectAiLevel}
            </if>
<!--            <if test="condition.submitUser != null and condition.submitUser!=''">-->
<!--                AND br.submit_user = #{condition.submitUser}-->
<!--            </if>-->
            <if test="condition.status != null">
                AND br.`status` = #{condition.status}
            </if>
            <if test="condition.buildingStatus != null">
                AND br.`building_status` = #{condition.buildingStatus}
            </if>

            <if test='condition.userCodes != null and condition.userCodes.size() > 0'>
                AND br.submit_user IN
                <foreach collection='condition.userCodes' item='code' open='(' separator=',' close=')'> #{code}</foreach>
            </if>

            <if test='condition.cityCodes != null and condition.cityCodes.size() > 0'>
                AND br.map_city IN
                <foreach collection='condition.cityCodes' item='code' open='(' separator=',' close=')'> #{code}</foreach>
            </if>

            <if test='condition.mapAdCodes != null and condition.mapAdCodes.size() > 0'>
                AND br.map_ad_code IN
                <foreach collection='condition.mapAdCodes' item='code' open='(' separator=',' close=')'> #{code} </foreach>
            </if>

            <if test='condition.buildingTypeList != null and condition.buildingTypeList.size() > 0'>
                AND br.building_type IN
                <foreach collection='condition.buildingTypeList' item='code' open='(' separator=',' close=')'> #{code}</foreach>
            </if>
        </where>
        order by br.create_time desc


    </select>

    <select id="listByMapNos" resultType="com.coocaa.meht.api.cheese.common.db.entity.BuildingRatingEntity">
        SELECT
        t.*
        FROM
        building_rating t
        JOIN ( SELECT map_no, MAX( create_time ) AS max_create_time FROM building_rating GROUP BY map_no ) sub ON t.map_no = sub.map_no
        AND t.create_time = sub.max_create_time
        <if test='mapNos != null and mapNos.size() > 0'>
            WHERE t.map_no IN
            <foreach collection='mapNos' item='mapNo' open='(' separator=',' close=')'>
                #{mapNo}
            </foreach>
        </if>
    </select>

    <select id="listCustomerPage"
            resultType="com.coocaa.meht.api.cheese.common.db.entity.BuildingRatingEntity">
        select
        br.`id`,
        br.building_name,
        br.building_no,
        br.building_type,
        br.map_city,
        br.map_address,
        COALESCE(NULLIF(br.project_review_level, ''), br.project_level) as project_level,
        br.target_point_count,
        br.building_status,
        br.submit_user,
        br.create_time,
        br.approve_time,
        bg.spec,
        br.create_by,
        br.status,
        IFNULL(su.real_name, ap.emp_name) as submit_user_name
        from building_rating br
        left join building_gene bg on bg.building_rating_no = br.building_no
        left join sys_user su on su.emp_code = br.submit_user
        left join agent_personnel ap on ap.emp_code = br.submit_user
        <where>
            and br.high_sea_flag = 0
            <if test="condition.buildingName != null and condition.buildingName!=''">
                and br.building_name like concat('%',#{condition.buildingName},'%')
            </if>
            <if test="condition.buildingNo != null and condition.buildingNo!=''">
                and br.building_no like concat('%',#{condition.buildingNo},'%')
            </if>
            <if test="condition.buildingType != null">
                and br.building_type = #{condition.buildingType}
            </if>
            <if test="condition.projectLevel != null and condition.projectLevel!=''">
                and (
                (br.project_review_level IS NOT NULL
                AND br.project_review_level != ''
                AND br.project_review_level = #{condition.projectLevel})
                OR
                ( (br.project_review_level IS NULL OR br.project_review_level = '')
                AND br.project_level = #{condition.projectLevel})
                )
            </if>
            <if test="condition.createTimeBegin != null and condition.createTimeBegin!=''">
                and br.create_time &gt;= #{condition.createTimeBegin}
            </if>
            <if test="condition.createTimeEnd != null and condition.createTimeEnd!=''">
                and br.create_time &lt;= #{condition.createTimeEnd}
            </if>
            <if test="condition.approveTimeBegin != null and condition.approveTimeBegin!=''">
                and br.approve_time &gt;= #{condition.approveTimeBegin}
            </if>
            <if test="condition.approveTimeEnd != null and condition.approveTimeEnd!=''">
                and br.approve_time &lt;= #{condition.approveTimeEnd}
            </if>
            <if test='condition.statuses != null and condition.statuses.size() > 0'>
                and br.`status` in
                <foreach collection='condition.statuses' item='status' open='(' separator=',' close=')'>
                    #{status}
                </foreach>
            </if>
            <if test='condition.cities != null and condition.cities.size() > 0'>
                and br.map_city in
                <foreach collection='condition.cities' item='city' open='(' separator=',' close=')'>
                    #{city}
                </foreach>
            </if>
            <if test='condition.submitUsers != null and condition.submitUsers.size() > 0'>
                and br.submit_user in
                <foreach collection='condition.submitUsers' item='user' open='(' separator=',' close=')'>
                    #{user}
                </foreach>
            </if>
            order by br.create_time desc
        </where>
    </select>


    <select id="getBusinessRatingMap"
            resultType="com.coocaa.meht.api.cheese.common.tools.bean.common.CodeNameVO">
        SELECT bo.code as code, br.map_city as name
        from building_rating br
        left JOIN business_opportunity bo on bo.building_no = br.building_no
        <if test='businessCodeList != null and businessCodeList.size() > 0'>
            WHERE bo.code in
            <foreach collection='businessCodeList' item='item' open='(' separator=',' close=')'>
                #{item}
            </foreach>
        </if>
    </select>
<!--    <select id="getBuildingInfoVOList"-->
<!--            resultType="com.coocaa.meht.api.cheese.common.db.dto.TempBuildingContractDTO">-->
<!--        SELECT-->
<!--            SUBSTRING_INDEX(project_code, '-', 1) as buildingNumber,-->
<!--            GROUP_CONCAT(DISTINCT contract_code SEPARATOR ' , ') AS contractCodes-->
<!--        FROM-->
<!--            temp_building_contract-->
<!--        GROUP BY-->
<!--            SUBSTRING_INDEX(project_code, '-', 1);-->
<!--    </select>-->

    <select id="listHighSeaCustomer"
            resultType="com.coocaa.meht.api.cheese.common.db.entity.BuildingRatingEntity">
        select
        br.`id`,
        br.building_name,
        br.building_no,
        br.building_type,
        br.map_city,
        br.map_address,
        COALESCE(NULLIF(br.project_review_level, ''), br.project_level) as project_level,
        br.target_point_count,
        br.create_time,
        br.enter_sea_time,
        br.enter_sea_reason,
        temp.responsible_person as submit_user
        from building_rating br
        left join (
            select hr.building_no, hr.responsible_person from high_sea_record hr where hr.id in
            (select max(id) from high_sea_record where operate_type = 1 group by building_no)
        ) temp on temp.building_no = br.building_no
        <where>
            and br.high_sea_flag = 1
            and br.`status` in (0, 1, 2)
            <if test="condition.buildingName != null and condition.buildingName!=''">
                and br.building_name like concat('%',#{condition.buildingName},'%')
            </if>
            <if test="condition.buildingNo != null and condition.buildingNo!=''">
                and br.building_no like concat('%',#{condition.buildingNo},'%')
            </if>
            <if test="condition.buildingType != null">
                and br.building_type = #{condition.buildingType}
            </if>
            <if test="condition.projectLevel != null and condition.projectLevel!=''">
                and (
                (br.project_review_level IS NOT NULL
                AND br.project_review_level != ''
                AND br.project_review_level = #{condition.projectLevel})
                OR
                ( (br.project_review_level IS NULL OR br.project_review_level = '')
                AND br.project_level = #{condition.projectLevel})
                )
            </if>
            <if test="condition.enterSeaTimeBegin != null and condition.enterSeaTimeBegin!=''">
                and br.enter_sea_time &gt;= #{condition.enterSeaTimeBegin}
            </if>
            <if test="condition.enterSeaTimeEnd != null and condition.enterSeaTimeEnd!=''">
                and br.enter_sea_time &lt;= #{condition.enterSeaTimeEnd}
            </if>
            <if test='condition.cities != null and condition.cities.size() > 0'>
                and br.map_city in
                <foreach collection='condition.cities' item='city' open='(' separator=',' close=')'>
                    #{city}
                </foreach>
            </if>
            <if test='condition.submitUsers != null and condition.submitUsers.size() > 0'>
                and temp.responsible_person in
                <foreach collection='condition.submitUsers' item='user' open='(' separator=',' close=')'>
                    #{user}
                </foreach>
            </if>
            order by br.enter_sea_time desc, br.id desc
        </where>
    </select>

</mapper>
