<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.TaskScoreDetailMapper">
    <resultMap id="BaseResultMap" type="com.coocaa.meht.api.cheese.common.db.entity.TaskScoreDetailEntity">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="city_name" property="cityName"/>
        <result column="building_name" property="buildingName"/>
        <result column="building_type" property="buildingType"/>
        <result column="building_level" property="buildingLevel"/>
        <result column="building_code" property="buildingCode"/>
        <result column="building_score" property="buildingScore"/>
        <result column="office_level" property="officeLevel"/>
        <result column="office_score" property="officeScore"/>
        <result column="location_level" property="locationLevel"/>
        <result column="location_score" property="locationScore"/>
        <result column="floor_count" property="floorCount"/>
        <result column="floor_score" property="floorScore"/>
        <result column="month_rent" property="monthRent"/>
        <result column="rent_score" property="rentScore"/>
        <result column="building_age" property="buildingAge"/>
        <result column="age_score" property="ageScore"/>
        <result column="facade_material" property="facadeMaterial"/>
        <result column="facade_score" property="facadeScore"/>
        <result column="lobby_level" property="lobbyLevel"/>
        <result column="lobby_score" property="lobbyScore"/>
        <result column="parking_level" property="parkingLevel"/>
        <result column="parking_score" property="parkingScore"/>
        <result column="comprehensive_brand" property="comprehensiveBrand"/>
        <result column="comprehensive_brand_score" property="comprehensiveBrandScore"/>
        <result column="dazhong_score" property="dazhongScore"/>
        <result column="review_score" property="reviewScore"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
</mapper> 