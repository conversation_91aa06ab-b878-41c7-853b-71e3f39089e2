<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.UserTagMapper">
    <select id="getFollowBusinessCount" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT br.building_no) AS total
        FROM building_rating br
                 INNER JOIN business_opportunity bo on bo.building_no = br.building_no
        WHERE br.submit_user = #{empCode}
          AND br.deleted = 0
          AND br.high_sea_flag = 0
          AND br.`status` in (0, 1, 2)
          AND NOT EXISTS (
            SELECT 1
            FROM building_status_change_log bsc
            WHERE
                bsc.biz_code = bo.code
              AND bsc.type = '0042-4'
              AND bsc.status  IN ('0043-5', '0043-6')
        )
    </select>
</mapper>