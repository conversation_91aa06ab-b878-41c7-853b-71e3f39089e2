<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.SysUserMapper">
    <select id="getUserListByCode" resultType="com.coocaa.meht.api.cheese.common.db.entity.SysUserEntity">
        SELECT real_name ,emp_code ,0 user_type, org_code belong_code, assess_dept belong_name,mobile
        FROM sys_user
        <if test='userCodes != null and userCodes.size() > 0'>
            WHERE
            emp_code IN
            <foreach collection='userCodes' item='code' open='(' separator=',' close=')'>
                #{code}
            </foreach>
        </if>
        UNION
        SELECT emp_name real_name,emp_code,1 user_type,agent_code belong_code, agent_name belong_name,emp_mobile mobile
        FROM agent_personnel
        <if test='userCodes != null and userCodes.size() > 0'>
            WHERE
            emp_code IN
            <foreach collection='userCodes' item='code' open='(' separator=',' close=')'>
                #{code}
            </foreach>
        </if>
    </select>

    <select id="getUserDetail" resultType="com.coocaa.meht.api.cheese.common.db.bean.SysUserApproveVO">
        SELECT
            su.id as id,
            su.emp_code as empCode,
            su.real_name as realName,
            su.mobile as mobile,
            su.fs_user_id as fsUserId,
            su.fs_open_id as fsOpenId,
            su.fs_union_id as fsUnionId,
            pa.approval_code as approvalCode,
            pa.approval_name as approvalName,
            pa.three_org as threeOrg,
            pa.four_org as fourOrg,
            pa.price_apply_code as priceApplyCode,
            pa.price_apply_name as priceApplyName,
            pa.contract_apply_code as contractApplyCode,
            pa.contract_apply_name as contractApplyName
        FROM
            personnel_approval pa
                LEFT JOIN sys_user su ON pa.initiate_approval_code = su.emp_code
        WHERE pa.emp_code = #{userCode}
    </select>
</mapper>
