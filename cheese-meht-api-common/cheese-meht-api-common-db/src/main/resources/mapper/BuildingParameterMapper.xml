<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.BuildingParameterMapper">

    <select id="getBuildingType"
            resultType="com.coocaa.meht.api.cheese.common.tools.bean.common.BuildingTypesDto">
        select distinct building_type as id ,case when building_type = 0 then '写字楼'
                                                  when building_type = 1 then '商住楼'
                                                  when building_type = 2 then '综合体'
                                                  when building_type = 3 then '产业园区'
                                                  else 0 end parameter_name
        from building_parameter t1
        where deleted = 0
        order by `building_type` asc;
    </select>

    <select id="getBuilding"
            resultType="com.coocaa.meht.api.cheese.common.tools.bean.common.BuildingParameterDto">
        select *
        from building_parameter t1
        where parent_id = 0
          and building_type =  #{buildingType}
          and deleted = 0
        order by sort asc;
    </select>

    <select id="getBuildingByParentId"
            resultType="com.coocaa.meht.api.cheese.common.tools.bean.common.BuildingParameterDto">
        select *
        from building_parameter t1
        where parent_id = #{parentId}
          and building_type =  #{buildingType}
          and deleted = 0
        order by sort asc;
    </select>
</mapper>
