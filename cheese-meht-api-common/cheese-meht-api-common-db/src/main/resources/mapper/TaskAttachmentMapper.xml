<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.TaskAttachmentMapper">
    <resultMap id="BaseResultMap" type="com.coocaa.meht.api.cheese.common.db.entity.TaskAttachmentEntity">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="cos_url" property="cosUrl"/>
        <result column="create_time" property="createTime"/>
    </resultMap>
</mapper> 