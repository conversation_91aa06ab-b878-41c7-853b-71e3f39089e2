<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.PointContractSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.coocaa.meht.api.cheese.common.db.entity.PointContractSnapshotEntity">
        <id column="id" property="id" />
        <result column="waiting_hall_id" property="waitingHallId" />
        <result column="code" property="code" />
        <result column="point_status" property="pointStatus" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="expire_time" property="expireTime" />
        <result column="device_size" property="deviceSize" />
        <result column="building_rating_no" property="buildingRatingNo" />
        <result column="city" property="city" />
        <result column="number" property="number" />
        <result column="building_name" property="buildingName" />
        <result column="unit_name" property="unitName" />
        <result column="floor" property="floor" />
        <result column="waiting_hall_name" property="waitingHallName" />
        <result column="waiting_hall_type" property="waitingHallType" />
        <result column="point_id" property="pointId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, waiting_hall_id, code, point_status, remark, create_by, create_time, update_by, update_time, expire_time, device_size, building_rating_no, city, number, building_name, unit_name, floor, waiting_hall_name, waiting_hall_type, point_id
    </sql>

</mapper> 