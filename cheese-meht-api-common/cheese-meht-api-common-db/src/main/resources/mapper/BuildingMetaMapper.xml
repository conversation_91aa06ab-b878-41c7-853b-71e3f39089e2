<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.BuildingMetaMapper">

    <select id="getManagerInfoByBuildingMetaNos"
            resultType="com.coocaa.meht.api.cheese.common.db.entity.BuildingMetaEntity">
        SELECT DISTINCT
        building_meta_no,
        manager
        FROM
        building_meta
        WHERE
        manager IS NOT NULL
        AND LENGTH( manager ) > 0
    </select>

    <select id="selectMetaGeneData" resultType="com.coocaa.meht.api.cheese.common.db.dto.MetaGeneDTO">
        SELECT
        bm.building_meta_no,
        bm.building_rating_no,
        bm.building_type,
        bm.building_name,
        bm.map_province,
        bm.map_city,
        bm.map_region,
        bm.map_address,
        bm.map_latitude,
        bm.map_longitude,
        bm.building_score,
        bm.project_level,
        bm.project_level_ai,
        bm.building_ai_score,
        bg.monthly_avg_price,
        bg.daily_price,
        bg.house_price,
        bg.competitive_media_info,
        bg.target_point_count,
        bg.max_floor_count,
        bg.building_age,
        bg.total_building_count,
        bg.total_unit_count,
        bg.total_waiting_count,
        bg.elevator_count,
        bg.parking_count,
        bg.forbidden_industry,
        bg.flow_count,
        bg.visit_count,
        bg.company_count,
        bg.occupancy_rate,
        bg.building_spacing,
        bg.building_ceiling_height,
        bg.property_fee,
        bg.min_floor_count,
        bg.delivery_date,
        bg.coverage_count
        from building_meta bm
        join building_rating br on bm.building_rating_no = br.building_no
        left join building_gene bg on bm.building_rating_no = bg.building_rating_no
        <where>
            <if test="condition.buildingTypes != null and condition.buildingTypes.size() > 0">
                and bm.building_type in
                <foreach item="item" collection="condition.buildingTypes" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.buildingStatus != null and condition.buildingStatus.size() > 0">
                and bm.building_status in
                <foreach item="item" collection="condition.buildingStatus" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.screenType != null">
                <choose>
                    <when test="condition.screenType == 1">
                        and br.large_screen_rating_flag = 0
                    </when>
                    <otherwise>
                        and br.large_screen_rating_flag = 1
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

</mapper>
