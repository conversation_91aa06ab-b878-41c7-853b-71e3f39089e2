<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.SysConfigMapper">
    
    <resultMap id="BaseResultMap" type="com.coocaa.meht.api.cheese.common.db.entity.SysConfigEntity">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="key" property="key"/>
        <result column="value" property="value"/>
        <result column="status" property="status"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

</mapper> 