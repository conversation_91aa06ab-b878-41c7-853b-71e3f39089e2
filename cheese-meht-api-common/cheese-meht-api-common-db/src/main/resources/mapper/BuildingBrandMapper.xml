<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.BuildingBrandMapper">
  <resultMap id="BaseResultMap" type="com.coocaa.meht.api.cheese.common.db.entity.BuildingBrandEntity">
    <!--@mbg.generated-->
    <!--@Table building_brand-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="index" jdbcType="DECIMAL" property="index" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="rank" jdbcType="INTEGER" property="rank" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_by, create_time, `index`, `name`, `rank`, `status`, `type`, update_by, 
    update_time
  </sql>
</mapper>