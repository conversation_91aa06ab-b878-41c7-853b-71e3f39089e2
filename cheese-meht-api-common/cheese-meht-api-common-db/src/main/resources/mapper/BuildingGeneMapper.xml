<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.BuildingGeneMapper">

    <select id="geneImport" resultType="com.coocaa.meht.api.cheese.common.db.bean.GeneImportDTO">
        SELECT
        a.map_city,
        a.map_province,
        a.map_region,
        a.building_meta_no,
        a.building_name,
        a.building_rating_no,
        CASE
            WHEN a.building_type = 0 THEN '写字楼'
            WHEN a.building_type = 1 THEN '商住楼'
            WHEN a.building_type = 2 THEN '综合体'
            WHEN a.building_type = 3 THEN '产业园区'
            ELSE '未知类型'
            END AS buildingTypeName,
        b.id as geneId,
        b.max_floor_count,
        b.parking_count,
        b.daily_price
        FROM
        building_meta a
        LEFT JOIN building_gene b ON a.building_rating_no = b.building_rating_no

        WHERE
        a.building_rating_no is not null
        and a.building_rating_no !=''

    </select>

</mapper> 