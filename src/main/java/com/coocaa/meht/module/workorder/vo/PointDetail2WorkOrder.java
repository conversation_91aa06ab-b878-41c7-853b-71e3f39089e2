package com.coocaa.meht.module.workorder.vo;

import com.coocaa.meht.module.web.dto.point.PointDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-08-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PointDetail2WorkOrder extends PointDetail {

    /**
     * 是否可安装
     */
    private Boolean canInstall;

    /**
     * 提示
     */
    private String message;

    /**
     * 合同状态 需要上传附件
     */
    private String contractStatus;

    /**
     * 是否需要上传附件
     */
    private Boolean requireAttachment;
}
