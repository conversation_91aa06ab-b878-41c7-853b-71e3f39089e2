# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build and Package
- `mvn clean compile` - Compile the project
- `mvn clean package` - Build the project and create JAR files
- `mvn clean install` - Build and install to local repository

### Running the Application
- Main application class: `com.coocaa.ad.cheese.cms.CheeseCmsApplication`
- Run with Maven: `mvn spring-boot:run -pl cheese-cms-api`
- Run with profiles: `mvn spring-boot:run -pl cheese-cms-api -Dspring-boot.run.profiles=dev`

### Testing
- Run tests: `mvn test`
- Run tests for specific module: `mvn test -pl cheese-cms-api`

### Environment Profiles
- `dev` - Development environment (default)
- `release` - Testing/Release environment
- `master` - Production environment

Use profiles with: `mvn clean package -P{profile-name}`

## Architecture Overview

This is a Spring Boot 3.2.9 microservice application for a contract management system (合同系统).

### Project Structure
- **cheese-cms-api**: Main application module containing REST controllers, services, and business logic
- **cheese-cms-common**: Common utilities and shared components
  - **cheese-cms-common-core**: Core utilities
  - **cheese-cms-common-db**: Database entities, mappers, and data access layer
  - **cheese-cms-common-generator**: Code generation utilities
  - **cheese-cms-common-tools**: Shared tools and utilities

### Key Technologies
- **Spring Boot 3.2.9** with Java 17
- **Spring Cloud 2023.0.0** with Alibaba components
- **MyBatis Plus 3.5.7** for data persistence
- **MySQL 8.0.33** database
- **Nacos** for service discovery and configuration management
- **Redis** with JetCache for caching
- **OpenFeign** for service communication
- **MapStruct** for object mapping
- **Knife4j/SpringDoc** for API documentation

### Main Business Domains
Located in `cheese-cms-api/src/main/java/com/coocaa/ad/cheese/cms/`:

- **venue**: Core contract and venue management
  - Contract lifecycle management (apply, agreement, amendment, abnormal handling)
  - Building and point management
  - Supplier management
  - Kanban/dashboard features
  - Ledger and financial tracking

- **dataimport**: Data import and synchronization services
  - Contract import functionality
  - Supplier data import
  - Point size and price synchronization

- **cos**: Tencent Cloud Object Storage integration
- **download**: Export and download services for various data types

### Database Layer
- MyBatis mappers located in `cheese-cms-common/cheese-cms-common-db/src/main/resources/mapper/`
- Entities and DAOs in the `common.db` package structure
- Uses MyBatis Plus for enhanced ORM capabilities

### Key Integrations
- **Feign Clients** for external service communication (CRM, SSP, SYS, etc.)
- **XXL-Job** for distributed scheduling
- **Prometheus** metrics for monitoring
- **Kafka** for message processing
- **JetCache** for distributed caching

### Configuration
- Environment-specific configs in `cheese-cms-api/configs/{env}/app.properties`
- Bootstrap configuration supports Nacos config center
- Profile-based resource filtering during build

## Development Notes

### Running Locally
The application expects Nacos configuration center to be available. Ensure proper configuration in bootstrap properties for your local environment.

### Common File Locations
- Main application: `cheese-cms-api/src/main/java/com/coocaa/ad/cheese/cms/CheeseCmsApplication.java`
- Controllers: `cheese-cms-api/src/main/java/com/coocaa/ad/cheese/cms/venue/controller/`
- Services: `cheese-cms-api/src/main/java/com/coocaa/ad/cheese/cms/venue/service/`
- Database mappers: `cheese-cms-common/cheese-cms-common-db/src/main/resources/mapper/venue/`
- SQL migrations: `docs/` directory contains version-specific SQL files

### API Documentation
Access Knife4j documentation at `/doc.html` when the application is running.